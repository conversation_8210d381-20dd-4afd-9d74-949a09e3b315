// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// DoaReadCollect is the golang structure for table doa_read_collect.
type DoaReadCollect struct {
	Id         uint   `json:"id"         orm:"id"          description:""`                 //
	UserId     uint   `json:"userId"     orm:"user_id"     description:"用户id"`             // 用户id
	Types      uint   `json:"types"      orm:"types"       description:"类型 1-doa,2-wirid"` // 类型 1-doa,2-wirid
	PId        uint   `json:"pId"        orm:"p_id"        description:"父级id"`             // 父级id
	BaccanId   uint   `json:"baccanId"   orm:"baccan_id"   description:"baccan_id"`        // baccan_id
	PName      string `json:"pName"      orm:"p_name"      description:"父级名称"`             // 父级名称
	BaccanName string `json:"baccanName" orm:"baccan_name" description:"名称"`               // 名称
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间（注册时间）"`       // 创建时间（注册时间）
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间，0代表创建后未更新"`   // 更新时间，0代表创建后未更新
}
