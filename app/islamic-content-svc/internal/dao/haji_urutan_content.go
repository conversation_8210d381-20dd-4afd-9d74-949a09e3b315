// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// hajiUrutanContentDao is the data access object for the table haji_urutan_content.
// You can define custom methods on it to extend its functionality as needed.
type hajiUrutanContentDao struct {
	*internal.HajiUrutanContentDao
}

var (
	// HajiUrutanContent is a globally accessible object for table haji_urutan_content operations.
	HajiUrutanContent = hajiUrutanContentDao{internal.NewHajiUrutanContentDao()}
)

// Add your custom methods and functionality below.
