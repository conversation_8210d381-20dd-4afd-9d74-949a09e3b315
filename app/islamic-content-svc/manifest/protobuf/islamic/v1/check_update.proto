syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";

message CheckUpdateReq {
  string platform = 1;      // 平台
  string current_version = 2;   // 当前版本
}

message CheckUpdateRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CheckUpdateData data = 4;
}

message CheckUpdateData{
  optional bool    is_update = 1;        // 是否需要更新
  optional bool    force_update = 2;     // 是否强制更新
  optional string  latest_version = 3;   // 最新版本
  optional string  update_url = 4;       // 下载地址
  optional int32   file_size = 5;        // 文件大小
  optional string  release_date = 6;     // 发布时间
  optional string   md5 = 7;              // 文件md5
}


service CheckUpdateService {
  // app 检查更新
  rpc CheckUpdate(CheckUpdateReq) returns (CheckUpdateRes);
}