// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiDoaPanjangBacaanContent {
    uint64 Id             = 1;  // 主键ID
    uint64 BacaanId       = 2;  // 诵读ID，关联haji_doa_panjang_bacaan.id
    int32  ContentOrder   = 3;  // 内容排序
    string Title          = 4;  // 内容标题（可为空）
    string MuqattaAt      = 5;  // Muqattaʿāt断章字母（有则展示，无不展示）
    string ArabicText     = 6;  // 阿拉伯文原文
    string IndonesianText = 7;  // 印尼语翻译
    string LatinText      = 8;  // 拉丁音译文本
    uint64 CreateTime     = 9;  // 创建时间（毫秒时间戳）
    uint64 UpdateTime     = 10; // 更新时间（毫秒时间戳）
}