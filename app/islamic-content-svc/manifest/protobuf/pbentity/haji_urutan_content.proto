// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiUrutanContent {
    uint64 Id            = 1; // 主键ID
    uint64 UrutanId      = 2; // 朝觐顺序ID，关联haji_urutan.id
    uint32 LanguageId    = 3; // 语言ID: 0-中文, 1-英文, 2-印尼语
    string UrutanName    = 4; // 朝觐仪式名称（最多60个字符）
    string UrutanTime    = 5; // 仪式时间
    string UrutanContent = 6; // 仪式内容描述（富文本）
    uint64 CreateTime    = 7; // 创建时间（毫秒时间戳）
    uint64 UpdateTime    = 8; // 更新时间（毫秒时间戳）
}