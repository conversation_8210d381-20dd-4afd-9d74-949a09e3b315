package consts

// Prefix/ModuleName/uuid.FileSuffix
// 例子 catalog/pub/ad/img/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg
const (
	FileDefaultBucketName = "halal-catalog" // 上传文件的bucket name
	FilePrefixPublicDir   = "pub"           // 公开目录
	FilePrefixInterDir    = "in"            // 内部目录
	FilePrefixTmpDir      = "tmp"           // 临时目录
	FilePrefixPrivate     = "private"       // 私有目录

)

// NOTE 新增常量后需要在ModuleName也加上!!!
const (
	FileModuleDefault         = "default"         // 上传文件模块-默认
	FileModuleSys             = "sys"             // 上传文件模块-系统内置,主要用于其他模块使用,使用时需要复制到对应的模块去,其他模块的数据表不能记录
	FileModulePhoneAreaCode   = "phoneAreaCode"   // 上传文件模块-区号
	FileModuleFile            = "file"            // 上传文件模块-文件
	FileModuleUserAppSettings = "userAppSettings" // 上传快速设置 推送提示-声音
	FileModuleAvatarCustom    = "avatarCustom"    // 上传文件模块 自定义用户头像
	FileModuleAvatarSystem    = "avatar"          // 上传文件模块 系统默认用户头像
	FileModuleFeedback        = "feedback"        // 上传文件模块 意见反馈
)

// ModuleName 模块
var ModuleName = map[string]struct{}{
	FileModuleDefault:         {}, // 默认目录
	FileModuleSys:             {}, // 系统内置图片(系统广告图、产品风格图、产品图标、产品皮肤)
	FileModulePhoneAreaCode:   {}, // 区号自定义上传的图片
	FileModuleFile:            {}, // 文件模块自定义上传的图片
	FileModuleUserAppSettings: {}, // 快速设置自定义上传推送提示-声音
	FileModuleAvatarCustom:    {}, // 自定义用户头像
	FileModuleAvatarSystem:    {}, // 系统默认用户头像
	FileModuleFeedback:        {}, // 意见反馈
}

const (
	FileTypeImage = "img"
	FileTypeVideo = "v"
	FileTypeBin   = "f"
)

// PrefixName 前缀
var PrefixName = map[string]struct{}{
	FilePrefixPublicDir: {},
	FilePrefixInterDir:  {},
	FilePrefixTmpDir:    {},
	FilePrefixPrivate:   {},
}

const (
	FileTypeS3Minio = "minio"
	FileTypeS3Aws   = "aws"
)
