// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiDoaPanjangBacaan {
    uint64 Id         = 1; // 主键ID
    uint64 DoaId      = 2; // 祈祷文ID，关联haji_doa_panjang.id
    int32  BacaanNo   = 3; // 诵读序号
    string BacaanName = 4; // 诵读名称
    uint64 CreateTime = 5; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 6; // 更新时间（毫秒时间戳）
}