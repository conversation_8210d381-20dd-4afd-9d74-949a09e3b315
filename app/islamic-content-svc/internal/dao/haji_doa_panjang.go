// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// hajiDoaPanjangDao is the data access object for the table haji_doa_panjang.
// You can define custom methods on it to extend its functionality as needed.
type hajiDoaPanjangDao struct {
	*internal.HajiDoaPanjangDao
}

var (
	// HajiDoaPanjang is a globally accessible object for table haji_doa_panjang operations.
	HajiDoaPanjang = hajiDoaPanjangDao{internal.NewHajiDoaPanjangDao()}
)

// Add your custom methods and functionality below.
