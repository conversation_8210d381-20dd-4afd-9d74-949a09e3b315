// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsDoaBacaan {
    uint32 Id         = 1; //
    uint32 DoaId      = 2; // doa_id
    string Name       = 3; // 名称
    string Content1   = 4; // 内容1
    string Content2   = 5; // 内容2
    string Content3   = 6; // 内容3
    int64  CreateTime = 7; // 创建时间（注册时间）
    int64  UpdateTime = 8; // 更新时间，0代表创建后未更新
    int32  Pid        = 9; // 父级id
}