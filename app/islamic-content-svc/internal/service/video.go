// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IVideo interface {
		// PlaylistList 获取视频播放列表
		PlaylistList(ctx context.Context, languageId uint32, page int, size int) (*model.VideoPlaylistListOutput, error)
		// VideoList 获取视频列表（支持分类、播放列表、搜索）
		VideoList(ctx context.Context, input *model.VideoListInput) (*model.VideoListOutput, error)
		// VideoDetail 获取视频详情
		VideoDetail(ctx context.Context, videoId uint32, languageId uint32, userId uint64) (*model.VideoDetailOutput, error)
		// RecommendedVideoList 获取推荐视频列表
		RecommendedVideoList(ctx context.Context, input *model.RecommendedVideoListInput) (*model.VideoListOutput, error)
		// VideoCollect 视频收藏/取消收藏
		VideoCollect(ctx context.Context, input *model.VideoCollectInput) error
		// VideoCollectList 获取用户收藏的视频列表
		VideoCollectList(ctx context.Context, userId uint64, languageId uint32, page int, size int) (*model.VideoListOutput, error)
		// VideoCollectStatusCheck 视频收藏状态检查
		VideoCollectStatusCheck(ctx context.Context, userId uint64, videoId uint32) (*model.VideoCollectStatusCheckOutput, error)
	}
)

var (
	localVideo IVideo
)

func Video() IVideo {
	if localVideo == nil {
		panic("implement not found for interface IVideo, forgot register?")
	}
	return localVideo
}

func RegisterVideo(i IVideo) {
	localVideo = i
}
