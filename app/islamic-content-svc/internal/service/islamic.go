// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
)

type (
	IIslamic interface {
		WiridList(ctx context.Context, in *v1.WiridListReq) (out *v1.WiridListRes)
		WiridBacaanList(ctx context.Context, in *v1.WiridBacaanListReq) (out *v1.WiridBacaanListRes)
		WiridBacaanInfoList(ctx context.Context, in *v1.WiridBacaanInfoListReq) (out *v1.WiridBacaanInfoListRes)
		DoaList(ctx context.Context, in *v1.DoaListReq) (out *v1.DoaListRes)
		DoaBacaanList(ctx context.Context, in *v1.DoaBacaanListReq) (out *v1.DoaBacaanListRes)
		DoaBacaanInfoList(ctx context.Context, in *v1.DoaBacaanInfoListReq) (out *v1.DoaBacaanInfoListRes)
		TahlilList(ctx context.Context, in *v1.TahlilListReq) (out *v1.TahlilListRes)
		SurahList(ctx context.Context, in *v1.SurahListReq) (out *v1.SurahListRes)
		JuzList(ctx context.Context, in *model.JuzParamInput) (out []*model.JuzParamOutput)
		QuerySurahByAyahId(ayahId int) (out *entity.SuratDaftar)
		AyahList(ctx context.Context, in *v1.AyahListReq) (out *v1.AyahListRes)
		AyahReadRecord(ctx context.Context, in *model.AyahReadRecordInput) (out []*model.AyahReadRecordOutput)
		AyahReadCollectList(ctx context.Context, in *v1.AyahReadCollectListReq) (out *v1.AyahReadCollectListRes)
		AyahReadCollect(ctx context.Context, in *model.AyahReadCollectInput) (out *model.AyahReadCollectOutput)
		CheckAyahReadCollectStatus(ctx context.Context, in *model.CheckAyahReadCollectStatusInput) (out *model.CheckAyahReadCollectStatusOutput)
		AyahReadRecordList(ctx context.Context, in *v1.AyahReadRecordListReq) (out *v1.AyahReadRecordListRes)
		NewsCategoryList(ctx context.Context, in *model.NewsCategoryListInput) (out []*model.NewsCategoryListOutput)
		NewsTopicList(ctx context.Context, in *model.NewsTopicListInput) (out []*model.NewsTopicListOutput)
		NewsListByCateId(ctx context.Context, in *v1.NewsListByCateIdReq) (out *v1.NewsListByCateIdRes)
		NewsInfo(ctx context.Context, in *model.NewsInfoInput) (out []*model.NewsInfoOutput)
		NewsListByTopicId(ctx context.Context, in *v1.NewsListByTopicIdReq) (out *v1.NewsListByTopicIdRes)
		NewsHotArticleIds(ctx context.Context, limit int) (articleIds []uint)
		NewsHotList(ctx context.Context, in *v1.NewsHotListReq) (out *v1.NewsHotListRes)
		NewsCollectArticleIds(ctx context.Context, limit int) (articleIds []uint)
		NewsCollectList(ctx context.Context, in *v1.NewsCollectReq) (out *v1.NewsCollectRes)
		NewsCollectStatusCheck(ctx context.Context, in *model.NewsCollectStatusCheckInput) (out *model.NewsCollectStatusCheckOutput)
		QueryArticleByArticleId(article int, language int) (out *entity.NewsArticleLanguage)
		NewsCollectOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput)
		NewsViewOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput)
		NewsShareOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput)
		// --------doa-----
		DoaReadCollectList(ctx context.Context, in *v1.DoaReadCollectListReq) (out *v1.DoaReadCollectListRes)
		QueryDoaByBaccanId(baccanId int) (doaInfo *entity.NewsDoa, doaBaccanInfo *entity.NewsDoaBacaan)
		QueryWiridByBaccanId(baccanId int) (wiridInfo *entity.NewsWirid, wiridBaccanInfo *entity.NewsWiridBacaan)
		DoaReadCollect(ctx context.Context, in *model.DoaReadCollectInput) (out *model.AyahReadCollectOutput)
		CheckDoaReadCollectStatus(ctx context.Context, in *model.CheckDoaReadCollectStatusInput) (out *model.CheckAyahReadCollectStatusOutput)
	}
)

var (
	localIslamic IIslamic
)

func Islamic() IIslamic {
	if localIslamic == nil {
		panic("implement not found for interface IIslamic, forgot register?")
	}
	return localIslamic
}

func RegisterIslamic(i IIslamic) {
	localIslamic = i
}
