// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiUrutan {
    uint64 Id         = 1; // 主键ID
    int32  UrutanNo   = 2; // 朝觐顺序（数字）
    string IconUrl    = 3; // 图标URL
    uint64 CreateTime = 4; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 5; // 更新时间（毫秒时间戳）
}