// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiUrutan is the golang structure of table haji_urutan for DAO operations like Where/Data.
type HajiUrutan struct {
	g.Meta     `orm:"table:haji_urutan, do:true"`
	Id         interface{} // 主键ID
	UrutanNo   interface{} // 朝觐顺序（数字）
	IconUrl    interface{} // 图标URL
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
