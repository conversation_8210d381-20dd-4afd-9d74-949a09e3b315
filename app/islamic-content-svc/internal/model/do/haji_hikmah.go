// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiHikmah is the golang structure of table haji_hikmah for DAO operations like Where/Data.
type HajiHikmah struct {
	g.Meta     `orm:"table:haji_hikmah, do:true"`
	Id         interface{} // 主键ID
	ArticleId  interface{} // 文章ID，关联news_article.id
	SortOrder  interface{} // 排序值，数字越小排序越靠前
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
