// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsCategory is the golang structure of table news_category for DAO operations like Where/Data.
type NewsCategory struct {
	g.Meta     `orm:"table:news_category, do:true"`
	Id         interface{} //
	ParentId   interface{} // 上级id，0表示顶级
	IsZh       interface{} // 是否中文，0-否，1-是
	IsEn       interface{} // 是否英文，0-否，1-是
	IsId       interface{} // 是否印尼文，0-否，1-是
	Status     interface{} // 状态，1启用，0关闭
	Sort       interface{} // 排序，数字越小，排序越靠前
	AdminId    interface{} // 分类负责人id
	CoverImgs  interface{} // 封面图
	Remark     interface{} // 备注
	Creater    interface{} // 创建者id
	CreateName interface{} // 创建者
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
	IsAppShow  interface{} // 是否app展示，1：是 0：否
}
