// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiHikmahLanguagesDao is the data access object for the table haji_hikmah_languages.
type HajiHikmahLanguagesDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  HajiHikmahLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// HajiHikmahLanguagesColumns defines and stores column names for the table haji_hikmah_languages.
type HajiHikmahLanguagesColumns struct {
	Id         string // 主键ID
	HikmahId   string // 朝觐智慧ID，关联haji_hikmah.id
	LanguageId string // 语言ID：0-中文，1-英文，2-印尼语
	Title      string // 标题
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiHikmahLanguagesColumns holds the columns for the table haji_hikmah_languages.
var hajiHikmahLanguagesColumns = HajiHikmahLanguagesColumns{
	Id:         "id",
	HikmahId:   "hikmah_id",
	LanguageId: "language_id",
	Title:      "title",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiHikmahLanguagesDao creates and returns a new DAO object for table data access.
func NewHajiHikmahLanguagesDao(handlers ...gdb.ModelHandler) *HajiHikmahLanguagesDao {
	return &HajiHikmahLanguagesDao{
		group:    "default",
		table:    "haji_hikmah_languages",
		columns:  hajiHikmahLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiHikmahLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiHikmahLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiHikmahLanguagesDao) Columns() HajiHikmahLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiHikmahLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiHikmahLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiHikmahLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
