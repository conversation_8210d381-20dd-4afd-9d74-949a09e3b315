// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IPrayer interface {
		// GetCalendar 获取日历数据
		GetCalendar(ctx context.Context, input *model.CalendarGetInput) ([]*model.CalendarDateInfo, error)
		// GetBatchCalendar 批量获取多个年月的日历数据
		GetBatchCalendar(ctx context.Context, input *model.BatchCalendarGetInput) (map[string][]*model.CalendarDateInfo, error)
		// GetDailyPrayerTime 获取每日祷告时间
		GetDailyPrayerTime(ctx context.Context, in *model.DailyPrayerTimeInput) (*model.PrayerTimeOutput, error)
		// GetMonthlyPrayerTimes 获取月度祷告时间
		GetMonthlyPrayerTimes(ctx context.Context, in *model.MonthlyPrayerTimesInput) ([]*model.PrayerTimeOutput, error)
		// GetHajiJadwalList 获取朝觐日程列表
		GetHajiJadwalList(ctx context.Context, languageId uint) (*model.HajiJadwalListOutput, error)
		// GetHajiJadwalDetail 获取朝觐日程详情
		GetHajiJadwalDetail(ctx context.Context, jadwalId uint64, languageId uint) (*model.HajiJadwalDetailOutput, error)
		// GetHajiUrutanList 获取朝觐仪式顺序列表
		GetHajiUrutanList(ctx context.Context, languageId uint) (*model.HajiUrutanListOutput, error)
		// GetHajiUrutanDetail 获取朝觐仪式顺序详情
		GetHajiUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.HajiUrutanDetailOutput, error)
		// GetHajiDoaRingkasList 获取朝觐祈祷文简要列表
		GetHajiDoaRingkasList(ctx context.Context) ([]*model.HajiDoaRingkasInfo, error)
		// GetHajiDoaPanjangList 获取朝觐祈祷文详细列表
		GetHajiDoaPanjangList(ctx context.Context) ([]*model.HajiDoaPanjangInfo, error)
		// GetHajiDoaPanjangBacaanList 获取朝觐祈祷文诵读内容列表
		GetHajiDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.HajiDoaPanjangBacaanListOutput, error)
		// GetHajiHikmahList 获取朝觐智慧列表
		GetHajiHikmahList(ctx context.Context, languageId uint) (*model.HajiHikmahListOutput, error)
	}
)

var (
	localPrayer IPrayer
)

func Prayer() IPrayer {
	if localPrayer == nil {
		panic("implement not found for interface IPrayer, forgot register?")
	}
	return localPrayer
}

func RegisterPrayer(i IPrayer) {
	localPrayer = i
}
