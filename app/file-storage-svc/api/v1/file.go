package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type FileUploadReq struct {
	g.Meta     `path:"/upload" method:"post" mime:"multipart/form-data" tags:"工具" summary:"上传文件"`
	File       *ghttp.UploadFile `json:"file" type:"file" dc:"选择上传文件"`
	FilePrefix string            `json:"filePrefix" d:"pub" dc:"目录前缀: pub 公开| private私有 | in 内部 | tmp 临时"`                                                                                 // 默认值 pub
	FileModule string            `json:"fileModule" d:"default" dc:"上传模块: avatar 默认头像 |avatarCustom 客户设置头像| feedback 意见反馈  | sys 系统内置 | phoneAreaCode 区号|file 文件|userAppSettings 推送提示-声音"` // 默认值 default
}

type FileUploadRes struct {
	Name       string `json:"name" dc:"图片的名称"`
	Key        string `json:"key" dc:"对象KEY"`
	BackendUrl string `json:"url" dc:"图片的后台访问地址"`
}

type GetBackUpUrlReq struct {
	Name string `json:"name" dc:"图片的名称"`
}

type GetBackUpUrlRes struct {
	Name string `json:"name" dc:"图片的名称"`
}

type MultiUploadReq struct { // 批量上传 // multiple
	g.Meta     `path:"/multi_upload" method:"post" mime:"multipart/form-data" tags:"工具" summary:"多个上传文件"`
	File       []*ghttp.UploadFile `json:"file" type:"file" dc:"选择上传文件"`
	FilePrefix string              `json:"filePrefix" d:"pub" dc:"目录前缀: pub 公开| private私有 | in 内部 | tmp 临时"`                                                                                 // 默认值 pub
	FileModule string              `json:"fileModule" d:"default" dc:"上传模块: avatar 默认头像 |avatarCustom 客户设置头像| feedback 意见反馈  | sys 系统内置 | phoneAreaCode 区号|file 文件|userAppSettings 推送提示-声音"` // 默认值 default
}

type MultiUploadRes struct {
	List []*FileUploadRes
}

//type SetPrivateReq struct {
//	g.Meta `path:"/set_private" method:"post"  tags:"工具" summary:"设置目录为私有"`
//	Path   string `json:"path" dc:"目录"`
//}
//
//type SetPrivateRes struct {
//}
