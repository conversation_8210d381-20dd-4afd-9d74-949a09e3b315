// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiUrutanDao is the data access object for the table haji_urutan.
type HajiUrutanDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  HajiUrutanColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// HajiUrutanColumns defines and stores column names for the table haji_urutan.
type HajiUrutanColumns struct {
	Id         string // 主键ID
	UrutanNo   string // 朝觐顺序（数字）
	IconUrl    string // 图标URL
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiUrutanColumns holds the columns for the table haji_urutan.
var hajiUrutanColumns = HajiUrutanColumns{
	Id:         "id",
	UrutanNo:   "urutan_no",
	IconUrl:    "icon_url",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiUrutanDao creates and returns a new DAO object for table data access.
func NewHajiUrutanDao(handlers ...gdb.ModelHandler) *HajiUrutanDao {
	return &HajiUrutanDao{
		group:    "default",
		table:    "haji_urutan",
		columns:  hajiUrutanColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiUrutanDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiUrutanDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiUrutanDao) Columns() HajiUrutanColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiUrutanDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiUrutanDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiUrutanDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
