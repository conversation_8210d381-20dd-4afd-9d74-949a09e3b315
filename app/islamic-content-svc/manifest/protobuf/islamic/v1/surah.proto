syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "pbentity/surat_ayat.proto";
import "pbentity/surat_daftar.proto";
import "pbentity/surat_tafsir.proto";
import "pbentity/news_tahlil.proto";
import "common/front_info.proto";
import "common/base.proto";
import "google/protobuf/wrappers.proto";

// 古兰经-章-列表
message SurahListReq {

  uint32 id = 1;   // 章节id
  string name = 2;  //名称
  uint32 is_popular = 3;  // 是否热门
  common.PageRequest page = 4;  // 分页参数
}


message SuratDaftarInfo {
  uint32 Id = 1;
  uint32 Nomor = 2;
  string Nama = 3;
  string NamaLatin = 4;
  uint32 JumlahAyat = 5;
  string TempatTurun = 6;
  string Arti = 7;
  string Deskripsi = 8;
  string Audio = 9;
}
message SurahListResData {
  repeated SuratDaftarInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}

message SurahListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  SurahListResData data = 4;
}


//古兰经-节-列表
message JuzListReq {
  string name = 1;   // juz名称
}

message JuzInfo {
  uint32 start_surah_id = 1;// 开始章id
  string start_surah_name= 2;// 开始章name
  uint32 end_surah_id = 3;// 结束章id
  string end_surah_name= 4;// 结束章name
  uint32 start_ayah_id = 5;// 开始节id
  uint32 end_ayah_id = 6;// 结束节id
  string juz = 7;// juz名称
  string first_word = 8;// 对应经文的第一个单词
  uint32 sort = 9;// 对应经文的第一个单词
}

message JuzListResData {
  repeated JuzInfo list = 1;
}
message JuzListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  JuzListResData data = 4;
}



//古兰经-节-列表
message AyahListReq {
    uint32 id = 1;   // 节id
    uint32 surah_id = 2;  //章节id
    uint32 juz_id = 3;  //juz_id
    uint32 page_number = 4;  //page 页数量
    common.PageRequest page = 5;  // 分页参数

}

message SuratAyatInfo {
  uint32 Id = 1;// 章id
  uint32 AyatId = 2;// 章id
  uint32 SurahId = 3;// 章id
  uint32 Nomor = 4;// 章id
  string Tr= 5;// 章name
  string Idn= 6;// 章name
  string Ar= 7;// 章name
}
message AyahListResData {
  repeated SuratAyatInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message AyahListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AyahListResData data = 4;
}


//
////古兰经-章-信息
//message SurahInfoReq {
//  string id = 1;   // 节id
//  string name = 2;  //名称
//}
//message SurahInfoRes {
//  int32 code = 1;
//  string msg = 2;
//  common.Error error = 3;
//  pbentity.SuratDaftar data = 4;
//}
//
//
////古兰经-章-解释-信息
//message SurahDescReq {
//  string id = 1;  //章节id
//}
//message SurahDescRes {
//  int32 code = 1;
//  string msg = 2;
//  common.Error error = 3;
//  pbentity.SuratTafsir data = 4;
//}


message AyahReadRecordReq {
  uint32 ayah_id = 1;  //节id
  uint32 is_user_op = 2;  //是否用户操作，1-是，0-否
}
message AyahReadRecordRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}


message AyahReadCollectReq {
  uint32 ayah_id = 2;  //章节id
  uint32 is_add = 3;  //是否添加收藏，1-添加，0-取消收藏
}
message AyahReadCollectRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message CheckAyahReadCollectStatusReq {
  uint32 ayah_id = 1;  //章节id
}
message CheckAyahReadCollectStatusResData {
  int32 is_collect = 1;
}
message CheckAyahReadCollectStatusRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CheckAyahReadCollectStatusResData data = 4;
}



message ReadInfo {
  uint32 surah_id = 1;// 章id
  string surah_name= 2;// 章name
  uint32 ayah_id = 3;// 节id
  uint32 juz_id= 4;// juz-id

  string arti = 5;// 章节含义
  uint32 ayahs = 6;// ayah数量
  string first_word = 7;// 对应经文的第一个单词


}
message AyahReadRecordListReq {
  common.PageRequest page = 1;  // 分页参数

}

message AyahReadRecordListResData {
  repeated ReadInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message AyahReadRecordListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AyahReadRecordListResData data = 4;
}


message AyahReadCollectListReq {
  common.PageRequest page = 1;  // 分页参数
}
message AyahReadCollectListResData {
  repeated ReadInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message AyahReadCollectListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AyahReadCollectListResData data = 4;
}




message TahlilListReq {
  common.PageRequest page = 1;  // 分页参数
}

message NewsTahlilInfo {
  uint32 Id = 1;// 章id
  string Name= 2;// 章name
  string Content1= 3;// 章name
  string Content2= 4;// 章name
  string Content3= 5;// 章name
}
message TahlilListResData {
  repeated NewsTahlilInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}

message TahlilListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  TahlilListResData data = 4;
}




message WiridListReq {
  common.PageRequest page = 1;  // 分页参数
  string Name= 2;// name

}

message WiridInfo {
  uint32 Id = 1;// id
  string Name= 2;// name
  uint32 Bacaans= 3;// Bacaans数量
}
message WiridListResData {
  repeated WiridInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}

message WiridListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WiridListResData data = 4;
}

// BacaanList信息列表
message WiridBacaanListReq {
  common.PageRequest page = 1;  // 分页参数
  int32 wirid_id = 2;
  string name= 3;// 名称

}

message WiridBacaanList {
  uint32 number = 1;// id
  uint32 bacaanId = 2;// id
  uint32 pid= 3;// 父id
  string name= 4;// 名称
}
message WiridBacaanListResData {
  repeated WiridBacaanList list = 1;
  common.PageResponse page = 2;  // 分页参数
}
message WiridBacaanListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WiridBacaanListResData data = 4;
}



// BacaanInfo 信息列表
message WiridBacaanInfoListReq {
  common.PageRequest page = 1;  // 分页参数
  int32 bacaan_id = 2;
}

message WiridBacaanInfo {
  uint32 Id = 1;// id
  string Name= 2;// name
  string Content1= 3;// page参数
  string Content2= 4;// 经文
  string Content3= 5;// 解释
}
message WiridBacaanInfoListResData {
  repeated WiridBacaanInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}
message WiridBacaanInfoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WiridBacaanInfoListResData data = 4;
}




message DoaListReq {
  common.PageRequest page = 1;  // 分页参数
  string Name= 2;// name

}

message DoaInfo {
  uint32 Id = 1;// id
  string Name= 2;// name
  uint32 Bacaans= 3;// Bacaans数量
}
message DoaListResData {
  repeated DoaInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}

message DoaListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  DoaListResData data = 4;
}


// BacaanList信息列表
message DoaBacaanListReq {
  common.PageRequest page = 1;  // 分页参数
  int32 doa_id = 2;
  string name= 3;// 名称

}

message DoaBacaanList {
  uint32 number = 1;// id
  uint32 bacaanId = 2;// id
  uint32 pid= 3;// 父id
  string name= 4;// 名称
}
message DoaBacaanListResData {
  repeated DoaBacaanList list = 1;
  common.PageResponse page = 2;  // 分页参数
}
message DoaBacaanListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  DoaBacaanListResData data = 4;
}



// BacaanInfo 信息列表
message DoaBacaanInfoListReq {
  common.PageRequest page = 1;  // 分页参数
  int32 bacaan_id = 2;
}

message DoaBacaanInfo {
  uint32 Id = 1;// id
  string Name= 2;// name
  string Content1= 3;// page参数
  string Content2= 4;// 经文
  string Content3= 5;// 解释
}
message DoaBacaanInfoListResData {
  repeated DoaBacaanInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}
message DoaBacaanInfoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  DoaBacaanInfoListResData data = 4;
}







message DoaReadCollectReq {
  uint32 baccan_id = 1;  //子id
  uint32 types = 2;  //类型，1-doa，2-wirid
}
message DoaReadCollectRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message CheckDoaReadCollectStatusReq {
  uint32 baccan_id = 1;  //子id
  uint32 types = 2;  //类型，1-doa，2-wirid

}
message CheckDoaReadCollectStatusResData {
  int32 is_collect = 1;
}
message CheckDoaReadCollectStatusRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CheckDoaReadCollectStatusResData data = 4;
}



message DoaReadInfo {
  uint32 baccan_id = 1;// 子id
  string baccan_name= 2;// 子name
  string p_name = 3;// 父名称
  uint32 types= 4;// 类型，1-doa，2-wirid
}
message DoaReadCollectListReq {
  common.PageRequest page = 1;  // 分页参数
}

message DoaReadCollectListResData {
  repeated DoaReadInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message DoaReadCollectListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  DoaReadCollectListResData data = 4;
}


service SurahService {
  rpc SurahList(SurahListReq) returns (SurahListRes);//古兰经-章-列表
  rpc JuzList(JuzListReq) returns (JuzListRes);//古兰经-juz-列表
  rpc AyahList(AyahListReq) returns (AyahListRes);//古兰经-节-列表
//  rpc SurahInfo(SurahInfoReq) returns (SurahInfoRes);//古兰经-章-信息
//  rpc SurahDesc(SurahDescReq) returns (SurahDescRes);//古兰经-章-解释-信息

  rpc AyahReadRecord(AyahReadRecordReq) returns (AyahReadRecordRes);//阅读记录
  rpc AyahReadRecordList(AyahReadRecordListReq) returns (AyahReadRecordListRes);//阅读记录列表-数据回显


  rpc CheckAyahReadCollectStatus(CheckAyahReadCollectStatusReq) returns (CheckAyahReadCollectStatusRes);//阅读收藏状态
  rpc AyahReadCollect(AyahReadCollectReq) returns (AyahReadCollectRes);//阅读收藏
  rpc AyahReadCollectList(AyahReadCollectListReq) returns (AyahReadCollectListRes);//阅读收藏列表-数据回显
  rpc TahlilList(TahlilListReq) returns (TahlilListRes);//Tahlil-列表

  rpc WiridList(WiridListReq) returns (WiridListRes);//Wirid-列表
  rpc WiridBacaanList(WiridBacaanListReq) returns (WiridBacaanListRes);//Wirid-Bacaan 标题列表
  rpc WiridBacaanInfoList(WiridBacaanInfoListReq) returns (WiridBacaanInfoListRes);//Wirid-Bacaan 数据列表
  rpc DoaList(DoaListReq) returns (DoaListRes);//Doa-列表
  rpc DoaBacaanList(DoaBacaanListReq) returns (DoaBacaanListRes);//Doa-Bacaan-标题列表
  rpc DoaBacaanInfoList(DoaBacaanInfoListReq) returns (DoaBacaanInfoListRes);//Doa-Bacaan 数据列表



  rpc CheckDoaReadCollectStatus(CheckDoaReadCollectStatusReq) returns (CheckDoaReadCollectStatusRes);//阅读收藏状态
  rpc DoaReadCollect(DoaReadCollectReq) returns (DoaReadCollectRes);//阅读收藏
  rpc DoaReadCollectList(DoaReadCollectListReq) returns (DoaReadCollectListRes);//阅读收藏列表-数据回显
}
