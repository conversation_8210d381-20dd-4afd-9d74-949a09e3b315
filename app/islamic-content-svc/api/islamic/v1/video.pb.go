// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/video.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// playlist列表信息
type PlaylistItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaylistId    uint32                 `protobuf:"varint,1,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID"`       // 播放列表ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"播放列表名称（当前语言）"`                                // 播放列表名称（当前语言）
	ShortTitle    string                 `protobuf:"bytes,3,opt,name=short_title,json=shortTitle,proto3" json:"short_title,omitempty" dc:"播放列表短标题（当前语言）"` // 播放列表短标题（当前语言）
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"播放列表描述（当前语言）"`                  // 播放列表描述（当前语言）
	CoverUrl      string                 `protobuf:"bytes,5,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty" dc:"专题封面图片链接"`            // 专题封面图片链接
	VideoCount    uint32                 `protobuf:"varint,6,opt,name=video_count,json=videoCount,proto3" json:"video_count,omitempty" dc:"播放列表下视频数量"`    // 播放列表下视频数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistItem) Reset() {
	*x = PlaylistItem{}
	mi := &file_islamic_v1_video_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistItem) ProtoMessage() {}

func (x *PlaylistItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistItem.ProtoReflect.Descriptor instead.
func (*PlaylistItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{0}
}

func (x *PlaylistItem) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *PlaylistItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlaylistItem) GetShortTitle() string {
	if x != nil {
		return x.ShortTitle
	}
	return ""
}

func (x *PlaylistItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PlaylistItem) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *PlaylistItem) GetVideoCount() uint32 {
	if x != nil {
		return x.VideoCount
	}
	return 0
}

// playlist列表请求
type PlaylistListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistListReq) Reset() {
	*x = PlaylistListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistListReq) ProtoMessage() {}

func (x *PlaylistListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistListReq.ProtoReflect.Descriptor instead.
func (*PlaylistListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{1}
}

func (x *PlaylistListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *PlaylistListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// playlist列表响应数据
type PlaylistListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*PlaylistItem        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"播放列表列表"` // 播放列表列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistListResData) Reset() {
	*x = PlaylistListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistListResData) ProtoMessage() {}

func (x *PlaylistListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistListResData.ProtoReflect.Descriptor instead.
func (*PlaylistListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{2}
}

func (x *PlaylistListResData) GetList() []*PlaylistItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *PlaylistListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// playlist列表响应
type PlaylistListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *PlaylistListResData   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistListRes) Reset() {
	*x = PlaylistListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistListRes) ProtoMessage() {}

func (x *PlaylistListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistListRes.ProtoReflect.Descriptor instead.
func (*PlaylistListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{3}
}

func (x *PlaylistListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlaylistListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PlaylistListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PlaylistListRes) GetData() *PlaylistListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频列表项（用于列表展示的简化版本）
type VideoListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                         // 视频ID
	CategoryId    uint32                 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID"`                // 分类ID
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题（当前语言）"`                                       // 视频标题（当前语言）
	VideoCoverUrl string                 `protobuf:"bytes,4,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty" dc:"视频封面图片URL"` // 视频封面图片URL
	VideoDuration uint32                 `protobuf:"varint,5,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty" dc:"视频时长(秒)"`    // 视频时长(秒)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoListItem) Reset() {
	*x = VideoListItem{}
	mi := &file_islamic_v1_video_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListItem) ProtoMessage() {}

func (x *VideoListItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListItem.ProtoReflect.Descriptor instead.
func (*VideoListItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{4}
}

func (x *VideoListItem) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoListItem) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *VideoListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VideoListItem) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *VideoListItem) GetVideoDuration() uint32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

// 视频详情信息（完整版本）
type Video struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	VideoId          uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                                            // 视频ID
	CategoryId       uint32                 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID"`                                   // 分类ID
	Title            string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题（当前语言）"`                                                          // 视频标题（当前语言）
	Description      string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"视频描述（当前语言）"`                                              // 视频描述（当前语言）
	VideoCoverUrl    string                 `protobuf:"bytes,5,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty" dc:"视频封面图片URL"`                    // 视频封面图片URL
	VideoUrl         string                 `protobuf:"bytes,6,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty" dc:"视频文件URL"`                                       // 视频文件URL
	Author           string                 `protobuf:"bytes,7,opt,name=author,proto3" json:"author,omitempty" dc:"视频作者"`                                                              // 视频作者
	AuthorLogo       string                 `protobuf:"bytes,8,opt,name=author_logo,json=authorLogo,proto3" json:"author_logo,omitempty" dc:"作者头像URL"`                                 // 作者头像URL
	AuthorAuthStatus uint32                 `protobuf:"varint,9,opt,name=author_auth_status,json=authorAuthStatus,proto3" json:"author_auth_status,omitempty" dc:"作者认证状态：0-未认证，1-已认证"` // 作者认证状态：0-未认证，1-已认证
	PublishState     uint32                 `protobuf:"varint,10,opt,name=publish_state,json=publishState,proto3" json:"publish_state,omitempty" dc:"发布状态：0-待发布，1-已发布，2-已下线"`          // 发布状态：0-待发布，1-已发布，2-已下线
	IsCollected      bool                   `protobuf:"varint,11,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"当前用户是否已收藏（需要登录）"`                    // 当前用户是否已收藏（需要登录）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Video) Reset() {
	*x = Video{}
	mi := &file_islamic_v1_video_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{5}
}

func (x *Video) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *Video) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Video) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Video) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Video) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *Video) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Video) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *Video) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

func (x *Video) GetPublishState() uint32 {
	if x != nil {
		return x.PublishState
	}
	return 0
}

func (x *Video) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 视频列表请求（支持多种查询方式）
type VideoListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"`                         // 语言ID
	CategoryId    uint32                 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID（可选）"`                     // 分类ID（可选）
	PlaylistId    uint32                 `protobuf:"varint,3,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID（可选）"`                   // 播放列表ID（可选）
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题搜索（可选）"`                                                // 视频标题搜索（可选）
	SortBy        string                 `protobuf:"bytes,5,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty" dc:"排序方式：view_count, created_at, published_at"` // 排序方式：view_count, created_at, published_at
	SortOrder     string                 `protobuf:"bytes,6,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty" dc:"排序顺序：asc, desc"`                   // 排序顺序：asc, desc
	Page          *common.PageRequest    `protobuf:"bytes,7,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                        // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoListReq) Reset() {
	*x = VideoListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListReq) ProtoMessage() {}

func (x *VideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListReq.ProtoReflect.Descriptor instead.
func (*VideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{6}
}

func (x *VideoListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoListReq) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *VideoListReq) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *VideoListReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VideoListReq) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *VideoListReq) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

func (x *VideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频列表响应数据
type VideoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoListItem       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"视频列表"`                                  // 视频列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`                                  // 分页信息
	Playlist      *PlaylistBasicInfo     `protobuf:"bytes,3,opt,name=playlist,proto3" json:"playlist,omitempty" dc:"播放列表基本信息（当通过playlist_id查询时返回）"` // 播放列表基本信息（当通过playlist_id查询时返回）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoListResData) Reset() {
	*x = VideoListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListResData) ProtoMessage() {}

func (x *VideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListResData.ProtoReflect.Descriptor instead.
func (*VideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{7}
}

func (x *VideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *VideoListResData) GetPlaylist() *PlaylistBasicInfo {
	if x != nil {
		return x.Playlist
	}
	return nil
}

// 视频列表响应
type VideoListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *VideoListResData      `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoListRes) Reset() {
	*x = VideoListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListRes) ProtoMessage() {}

func (x *VideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListRes.ProtoReflect.Descriptor instead.
func (*VideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{8}
}

func (x *VideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoListRes) GetData() *VideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频详情请求
type VideoDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	VideoId       uint32                 `protobuf:"varint,2,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`          // 视频ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoDetailReq) Reset() {
	*x = VideoDetailReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoDetailReq) ProtoMessage() {}

func (x *VideoDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoDetailReq.ProtoReflect.Descriptor instead.
func (*VideoDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{9}
}

func (x *VideoDetailReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoDetailReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

// 视频详情响应
type VideoDetailRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *Video                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoDetailRes) Reset() {
	*x = VideoDetailRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoDetailRes) ProtoMessage() {}

func (x *VideoDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoDetailRes.ProtoReflect.Descriptor instead.
func (*VideoDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{10}
}

func (x *VideoDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoDetailRes) GetData() *Video {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频收藏请求
type VideoCollectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`         // 视频ID
	IsAdd         uint32                 `protobuf:"varint,2,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` // 是否添加收藏，1-添加，0-取消收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectReq) Reset() {
	*x = VideoCollectReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectReq) ProtoMessage() {}

func (x *VideoCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectReq.ProtoReflect.Descriptor instead.
func (*VideoCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{11}
}

func (x *VideoCollectReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

// 视频收藏响应
type VideoCollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectRes) Reset() {
	*x = VideoCollectRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectRes) ProtoMessage() {}

func (x *VideoCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectRes.ProtoReflect.Descriptor instead.
func (*VideoCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{12}
}

func (x *VideoCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 视频收藏列表请求
type VideoCollectListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectListReq) Reset() {
	*x = VideoCollectListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListReq) ProtoMessage() {}

func (x *VideoCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListReq.ProtoReflect.Descriptor instead.
func (*VideoCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{13}
}

func (x *VideoCollectListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频收藏列表响应数据
type VideoCollectListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoListItem       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"收藏的视频列表"` // 收藏的视频列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`    // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectListResData) Reset() {
	*x = VideoCollectListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListResData) ProtoMessage() {}

func (x *VideoCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListResData.ProtoReflect.Descriptor instead.
func (*VideoCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{14}
}

func (x *VideoCollectListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频收藏列表响应
type VideoCollectListRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *VideoCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectListRes) Reset() {
	*x = VideoCollectListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListRes) ProtoMessage() {}

func (x *VideoCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListRes.ProtoReflect.Descriptor instead.
func (*VideoCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{15}
}

func (x *VideoCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoCollectListRes) GetData() *VideoCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 播放列表基本信息（用于VideoList返回）
type PlaylistBasicInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaylistId    uint32                 `protobuf:"varint,1,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID"` // 播放列表ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"播放列表名称（当前语言）"`                          // 播放列表名称（当前语言）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistBasicInfo) Reset() {
	*x = PlaylistBasicInfo{}
	mi := &file_islamic_v1_video_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistBasicInfo) ProtoMessage() {}

func (x *PlaylistBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistBasicInfo.ProtoReflect.Descriptor instead.
func (*PlaylistBasicInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{16}
}

func (x *PlaylistBasicInfo) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *PlaylistBasicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 推荐视频列表请求
type RecommendedVideoListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"`     // 语言ID
	CategoryId    uint32                 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID（可选）"` // 分类ID（可选）
	Page          *common.PageRequest    `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                    // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendedVideoListReq) Reset() {
	*x = RecommendedVideoListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedVideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListReq) ProtoMessage() {}

func (x *RecommendedVideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListReq.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{17}
}

func (x *RecommendedVideoListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *RecommendedVideoListReq) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *RecommendedVideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 推荐视频列表响应数据
type RecommendedVideoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoListItem       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"推荐视频列表"` // 推荐视频列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendedVideoListResData) Reset() {
	*x = RecommendedVideoListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedVideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListResData) ProtoMessage() {}

func (x *RecommendedVideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListResData.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{18}
}

func (x *RecommendedVideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecommendedVideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 推荐视频列表响应
type RecommendedVideoListRes struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *RecommendedVideoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendedVideoListRes) Reset() {
	*x = RecommendedVideoListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedVideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListRes) ProtoMessage() {}

func (x *RecommendedVideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListRes.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{19}
}

func (x *RecommendedVideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RecommendedVideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RecommendedVideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RecommendedVideoListRes) GetData() *RecommendedVideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频收藏状态检查请求
type VideoCollectStatusCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"` // 视频ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectStatusCheckReq) Reset() {
	*x = VideoCollectStatusCheckReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectStatusCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectStatusCheckReq) ProtoMessage() {}

func (x *VideoCollectStatusCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectStatusCheckReq.ProtoReflect.Descriptor instead.
func (*VideoCollectStatusCheckReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{20}
}

func (x *VideoCollectStatusCheckReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

type VideoCollectStatusCheckData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsCollect     int32                  `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty" 1:"没收藏, 2: 已收藏"` // 1: 没收藏, 2: 已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectStatusCheckData) Reset() {
	*x = VideoCollectStatusCheckData{}
	mi := &file_islamic_v1_video_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectStatusCheckData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectStatusCheckData) ProtoMessage() {}

func (x *VideoCollectStatusCheckData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectStatusCheckData.ProtoReflect.Descriptor instead.
func (*VideoCollectStatusCheckData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{21}
}

func (x *VideoCollectStatusCheckData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

// 视频收藏状态检查响应
type VideoCollectStatusCheckRes struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *VideoCollectStatusCheckData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectStatusCheckRes) Reset() {
	*x = VideoCollectStatusCheckRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectStatusCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectStatusCheckRes) ProtoMessage() {}

func (x *VideoCollectStatusCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectStatusCheckRes.ProtoReflect.Descriptor instead.
func (*VideoCollectStatusCheckRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{22}
}

func (x *VideoCollectStatusCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectStatusCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectStatusCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoCollectStatusCheckRes) GetData() *VideoCollectStatusCheckData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_video_proto protoreflect.FileDescriptor

const file_islamic_v1_video_proto_rawDesc = "" +
	"\n" +
	"\x16islamic/v1/video.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\x1a\x1fpbentity/video_categories.proto\x1a'pbentity/video_category_languages.proto\x1a\x1dpbentity/video_collects.proto\x1a\x1epbentity/video_languages.proto\x1a!pbentity/video_play_history.proto\x1a\x1epbentity/video_playlists.proto\x1a'pbentity/video_playlist_languages.proto\x1a'pbentity/video_playlist_relations.proto\x1a\x1bpbentity/video_shares.proto\x1a\x15pbentity/videos.proto\x1a\x1egoogle/protobuf/wrappers.proto\"\xc4\x01\n" +
	"\fPlaylistItem\x12\x1f\n" +
	"\vplaylist_id\x18\x01 \x01(\rR\n" +
	"playlistId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vshort_title\x18\x03 \x01(\tR\n" +
	"shortTitle\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1b\n" +
	"\tcover_url\x18\x05 \x01(\tR\bcoverUrl\x12\x1f\n" +
	"\vvideo_count\x18\x06 \x01(\rR\n" +
	"videoCount\"[\n" +
	"\x0fPlaylistListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"m\n" +
	"\x13PlaylistListResData\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.islamic.v1.PlaylistItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x91\x01\n" +
	"\x0fPlaylistListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x123\n" +
	"\x04data\x18\x04 \x01(\v2\x1f.islamic.v1.PlaylistListResDataR\x04data\"\xb0\x01\n" +
	"\rVideoListItem\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\rR\n" +
	"categoryId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12&\n" +
	"\x0fvideo_cover_url\x18\x04 \x01(\tR\rvideoCoverUrl\x12%\n" +
	"\x0evideo_duration\x18\x05 \x01(\rR\rvideoDuration\"\xef\x02\n" +
	"\x05Video\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\rR\n" +
	"categoryId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12&\n" +
	"\x0fvideo_cover_url\x18\x05 \x01(\tR\rvideoCoverUrl\x12\x1b\n" +
	"\tvideo_url\x18\x06 \x01(\tR\bvideoUrl\x12\x16\n" +
	"\x06author\x18\a \x01(\tR\x06author\x12\x1f\n" +
	"\vauthor_logo\x18\b \x01(\tR\n" +
	"authorLogo\x12,\n" +
	"\x12author_auth_status\x18\t \x01(\rR\x10authorAuthStatus\x12#\n" +
	"\rpublish_state\x18\n" +
	" \x01(\rR\fpublishState\x12!\n" +
	"\fis_collected\x18\v \x01(\bR\visCollected\"\xe8\x01\n" +
	"\fVideoListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\rR\n" +
	"categoryId\x12\x1f\n" +
	"\vplaylist_id\x18\x03 \x01(\rR\n" +
	"playlistId\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x17\n" +
	"\asort_by\x18\x05 \x01(\tR\x06sortBy\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x06 \x01(\tR\tsortOrder\x12'\n" +
	"\x04page\x18\a \x01(\v2\x13.common.PageRequestR\x04page\"\xa6\x01\n" +
	"\x10VideoListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\x129\n" +
	"\bplaylist\x18\x03 \x01(\v2\x1d.islamic.v1.PlaylistBasicInfoR\bplaylist\"\x8b\x01\n" +
	"\fVideoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x01(\v2\x1c.islamic.v1.VideoListResDataR\x04data\"L\n" +
	"\x0eVideoDetailReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x19\n" +
	"\bvideo_id\x18\x02 \x01(\rR\avideoId\"\x82\x01\n" +
	"\x0eVideoDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12%\n" +
	"\x04data\x18\x04 \x01(\v2\x11.islamic.v1.VideoR\x04data\"C\n" +
	"\x0fVideoCollectReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12\x15\n" +
	"\x06is_add\x18\x02 \x01(\rR\x05isAdd\"\\\n" +
	"\x0fVideoCollectRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"_\n" +
	"\x13VideoCollectListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"r\n" +
	"\x17VideoCollectListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x99\x01\n" +
	"\x13VideoCollectListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.VideoCollectListResDataR\x04data\"H\n" +
	"\x11PlaylistBasicInfo\x12\x1f\n" +
	"\vplaylist_id\x18\x01 \x01(\rR\n" +
	"playlistId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x84\x01\n" +
	"\x17RecommendedVideoListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\rR\n" +
	"categoryId\x12'\n" +
	"\x04page\x18\x03 \x01(\v2\x13.common.PageRequestR\x04page\"v\n" +
	"\x1bRecommendedVideoListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\xa1\x01\n" +
	"\x17RecommendedVideoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12;\n" +
	"\x04data\x18\x04 \x01(\v2'.islamic.v1.RecommendedVideoListResDataR\x04data\"7\n" +
	"\x1aVideoCollectStatusCheckReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\"<\n" +
	"\x1bVideoCollectStatusCheckData\x12\x1d\n" +
	"\n" +
	"is_collect\x18\x01 \x01(\x05R\tisCollect\"\xa4\x01\n" +
	"\x1aVideoCollectStatusCheckRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12;\n" +
	"\x04data\x18\x04 \x01(\v2'.islamic.v1.VideoCollectStatusCheckDataR\x04data2\xcd\x04\n" +
	"\fVideoService\x12H\n" +
	"\fPlaylistList\x12\x1b.islamic.v1.PlaylistListReq\x1a\x1b.islamic.v1.PlaylistListRes\x12?\n" +
	"\tVideoList\x12\x18.islamic.v1.VideoListReq\x1a\x18.islamic.v1.VideoListRes\x12E\n" +
	"\vVideoDetail\x12\x1a.islamic.v1.VideoDetailReq\x1a\x1a.islamic.v1.VideoDetailRes\x12`\n" +
	"\x14RecommendedVideoList\x12#.islamic.v1.RecommendedVideoListReq\x1a#.islamic.v1.RecommendedVideoListRes\x12H\n" +
	"\fVideoCollect\x12\x1b.islamic.v1.VideoCollectReq\x1a\x1b.islamic.v1.VideoCollectRes\x12T\n" +
	"\x10VideoCollectList\x12\x1f.islamic.v1.VideoCollectListReq\x1a\x1f.islamic.v1.VideoCollectListRes\x12i\n" +
	"\x17VideoCollectStatusCheck\x12&.islamic.v1.VideoCollectStatusCheckReq\x1a&.islamic.v1.VideoCollectStatusCheckResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_video_proto_rawDescOnce sync.Once
	file_islamic_v1_video_proto_rawDescData []byte
)

func file_islamic_v1_video_proto_rawDescGZIP() []byte {
	file_islamic_v1_video_proto_rawDescOnce.Do(func() {
		file_islamic_v1_video_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_video_proto_rawDesc), len(file_islamic_v1_video_proto_rawDesc)))
	})
	return file_islamic_v1_video_proto_rawDescData
}

var file_islamic_v1_video_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_islamic_v1_video_proto_goTypes = []any{
	(*PlaylistItem)(nil),                // 0: islamic.v1.PlaylistItem
	(*PlaylistListReq)(nil),             // 1: islamic.v1.PlaylistListReq
	(*PlaylistListResData)(nil),         // 2: islamic.v1.PlaylistListResData
	(*PlaylistListRes)(nil),             // 3: islamic.v1.PlaylistListRes
	(*VideoListItem)(nil),               // 4: islamic.v1.VideoListItem
	(*Video)(nil),                       // 5: islamic.v1.Video
	(*VideoListReq)(nil),                // 6: islamic.v1.VideoListReq
	(*VideoListResData)(nil),            // 7: islamic.v1.VideoListResData
	(*VideoListRes)(nil),                // 8: islamic.v1.VideoListRes
	(*VideoDetailReq)(nil),              // 9: islamic.v1.VideoDetailReq
	(*VideoDetailRes)(nil),              // 10: islamic.v1.VideoDetailRes
	(*VideoCollectReq)(nil),             // 11: islamic.v1.VideoCollectReq
	(*VideoCollectRes)(nil),             // 12: islamic.v1.VideoCollectRes
	(*VideoCollectListReq)(nil),         // 13: islamic.v1.VideoCollectListReq
	(*VideoCollectListResData)(nil),     // 14: islamic.v1.VideoCollectListResData
	(*VideoCollectListRes)(nil),         // 15: islamic.v1.VideoCollectListRes
	(*PlaylistBasicInfo)(nil),           // 16: islamic.v1.PlaylistBasicInfo
	(*RecommendedVideoListReq)(nil),     // 17: islamic.v1.RecommendedVideoListReq
	(*RecommendedVideoListResData)(nil), // 18: islamic.v1.RecommendedVideoListResData
	(*RecommendedVideoListRes)(nil),     // 19: islamic.v1.RecommendedVideoListRes
	(*VideoCollectStatusCheckReq)(nil),  // 20: islamic.v1.VideoCollectStatusCheckReq
	(*VideoCollectStatusCheckData)(nil), // 21: islamic.v1.VideoCollectStatusCheckData
	(*VideoCollectStatusCheckRes)(nil),  // 22: islamic.v1.VideoCollectStatusCheckRes
	(*common.PageRequest)(nil),          // 23: common.PageRequest
	(*common.PageResponse)(nil),         // 24: common.PageResponse
	(*common.Error)(nil),                // 25: common.Error
}
var file_islamic_v1_video_proto_depIdxs = []int32{
	23, // 0: islamic.v1.PlaylistListReq.page:type_name -> common.PageRequest
	0,  // 1: islamic.v1.PlaylistListResData.list:type_name -> islamic.v1.PlaylistItem
	24, // 2: islamic.v1.PlaylistListResData.page:type_name -> common.PageResponse
	25, // 3: islamic.v1.PlaylistListRes.error:type_name -> common.Error
	2,  // 4: islamic.v1.PlaylistListRes.data:type_name -> islamic.v1.PlaylistListResData
	23, // 5: islamic.v1.VideoListReq.page:type_name -> common.PageRequest
	4,  // 6: islamic.v1.VideoListResData.list:type_name -> islamic.v1.VideoListItem
	24, // 7: islamic.v1.VideoListResData.page:type_name -> common.PageResponse
	16, // 8: islamic.v1.VideoListResData.playlist:type_name -> islamic.v1.PlaylistBasicInfo
	25, // 9: islamic.v1.VideoListRes.error:type_name -> common.Error
	7,  // 10: islamic.v1.VideoListRes.data:type_name -> islamic.v1.VideoListResData
	25, // 11: islamic.v1.VideoDetailRes.error:type_name -> common.Error
	5,  // 12: islamic.v1.VideoDetailRes.data:type_name -> islamic.v1.Video
	25, // 13: islamic.v1.VideoCollectRes.error:type_name -> common.Error
	23, // 14: islamic.v1.VideoCollectListReq.page:type_name -> common.PageRequest
	4,  // 15: islamic.v1.VideoCollectListResData.list:type_name -> islamic.v1.VideoListItem
	24, // 16: islamic.v1.VideoCollectListResData.page:type_name -> common.PageResponse
	25, // 17: islamic.v1.VideoCollectListRes.error:type_name -> common.Error
	14, // 18: islamic.v1.VideoCollectListRes.data:type_name -> islamic.v1.VideoCollectListResData
	23, // 19: islamic.v1.RecommendedVideoListReq.page:type_name -> common.PageRequest
	4,  // 20: islamic.v1.RecommendedVideoListResData.list:type_name -> islamic.v1.VideoListItem
	24, // 21: islamic.v1.RecommendedVideoListResData.page:type_name -> common.PageResponse
	25, // 22: islamic.v1.RecommendedVideoListRes.error:type_name -> common.Error
	18, // 23: islamic.v1.RecommendedVideoListRes.data:type_name -> islamic.v1.RecommendedVideoListResData
	25, // 24: islamic.v1.VideoCollectStatusCheckRes.error:type_name -> common.Error
	21, // 25: islamic.v1.VideoCollectStatusCheckRes.data:type_name -> islamic.v1.VideoCollectStatusCheckData
	1,  // 26: islamic.v1.VideoService.PlaylistList:input_type -> islamic.v1.PlaylistListReq
	6,  // 27: islamic.v1.VideoService.VideoList:input_type -> islamic.v1.VideoListReq
	9,  // 28: islamic.v1.VideoService.VideoDetail:input_type -> islamic.v1.VideoDetailReq
	17, // 29: islamic.v1.VideoService.RecommendedVideoList:input_type -> islamic.v1.RecommendedVideoListReq
	11, // 30: islamic.v1.VideoService.VideoCollect:input_type -> islamic.v1.VideoCollectReq
	13, // 31: islamic.v1.VideoService.VideoCollectList:input_type -> islamic.v1.VideoCollectListReq
	20, // 32: islamic.v1.VideoService.VideoCollectStatusCheck:input_type -> islamic.v1.VideoCollectStatusCheckReq
	3,  // 33: islamic.v1.VideoService.PlaylistList:output_type -> islamic.v1.PlaylistListRes
	8,  // 34: islamic.v1.VideoService.VideoList:output_type -> islamic.v1.VideoListRes
	10, // 35: islamic.v1.VideoService.VideoDetail:output_type -> islamic.v1.VideoDetailRes
	19, // 36: islamic.v1.VideoService.RecommendedVideoList:output_type -> islamic.v1.RecommendedVideoListRes
	12, // 37: islamic.v1.VideoService.VideoCollect:output_type -> islamic.v1.VideoCollectRes
	15, // 38: islamic.v1.VideoService.VideoCollectList:output_type -> islamic.v1.VideoCollectListRes
	22, // 39: islamic.v1.VideoService.VideoCollectStatusCheck:output_type -> islamic.v1.VideoCollectStatusCheckRes
	33, // [33:40] is the sub-list for method output_type
	26, // [26:33] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_islamic_v1_video_proto_init() }
func file_islamic_v1_video_proto_init() {
	if File_islamic_v1_video_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_video_proto_rawDesc), len(file_islamic_v1_video_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_video_proto_goTypes,
		DependencyIndexes: file_islamic_v1_video_proto_depIdxs,
		MessageInfos:      file_islamic_v1_video_proto_msgTypes,
	}.Build()
	File_islamic_v1_video_proto = out.File
	file_islamic_v1_video_proto_goTypes = nil
	file_islamic_v1_video_proto_depIdxs = nil
}
