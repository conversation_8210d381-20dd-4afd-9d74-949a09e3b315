// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// hajiDoaRingkasContentDao is the data access object for the table haji_doa_ringkas_content.
// You can define custom methods on it to extend its functionality as needed.
type hajiDoaRingkasContentDao struct {
	*internal.HajiDoaRingkasContentDao
}

var (
	// HajiDoaRingkasContent is a globally accessible object for table haji_doa_ringkas_content operations.
	HajiDoaRingkasContent = hajiDoaRingkasContentDao{internal.NewHajiDoaRingkasContentDao()}
)

// Add your custom methods and functionality below.
