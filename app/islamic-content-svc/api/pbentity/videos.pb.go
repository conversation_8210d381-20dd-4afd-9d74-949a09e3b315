// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/videos.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Videos struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                            // 主键ID
	CategoryId       uint32                 `protobuf:"varint,2,opt,name=CategoryId,proto3" json:"CategoryId,omitempty" dc:"分类ID"`                            // 分类ID
	VideoUrl         string                 `protobuf:"bytes,3,opt,name=VideoUrl,proto3" json:"VideoUrl,omitempty" dc:"视频文件URL"`                              // 视频文件URL
	VideoSize        uint64                 `protobuf:"varint,4,opt,name=VideoSize,proto3" json:"VideoSize,omitempty" dc:"视频文件大小(字节)"`                        // 视频文件大小(字节)
	VideoDuration    uint32                 `protobuf:"varint,5,opt,name=VideoDuration,proto3" json:"VideoDuration,omitempty" dc:"视频时长(秒)"`                   // 视频时长(秒)
	VideoFormat      string                 `protobuf:"bytes,6,opt,name=VideoFormat,proto3" json:"VideoFormat,omitempty" dc:"视频格式：mp4, mov等"`                 // 视频格式：mp4, mov等
	VideoCoverUrl    string                 `protobuf:"bytes,7,opt,name=VideoCoverUrl,proto3" json:"VideoCoverUrl,omitempty" dc:"视频封面图片URL"`                  // 视频封面图片URL
	ViewCount        uint64                 `protobuf:"varint,8,opt,name=ViewCount,proto3" json:"ViewCount,omitempty" dc:"播放次数"`                              // 播放次数
	ShareCount       uint64                 `protobuf:"varint,9,opt,name=ShareCount,proto3" json:"ShareCount,omitempty" dc:"分享次数"`                            // 分享次数
	CollectCount     uint64                 `protobuf:"varint,10,opt,name=CollectCount,proto3" json:"CollectCount,omitempty" dc:"收藏次数"`                       // 收藏次数
	CreatorName      string                 `protobuf:"bytes,11,opt,name=CreatorName,proto3" json:"CreatorName,omitempty" dc:"创建者姓名"`                         // 创建者姓名
	Author           string                 `protobuf:"bytes,12,opt,name=Author,proto3" json:"Author,omitempty" dc:"视频作者"`                                    // 视频作者
	AuthorLogo       string                 `protobuf:"bytes,13,opt,name=AuthorLogo,proto3" json:"AuthorLogo,omitempty" dc:"作者头像URL"`                         // 作者头像URL
	AuthorAuthStatus uint32                 `protobuf:"varint,14,opt,name=AuthorAuthStatus,proto3" json:"AuthorAuthStatus,omitempty" dc:"作者认证状态：0-未认证，1-已认证"` // 作者认证状态：0-未认证，1-已认证
	PublishState     uint32                 `protobuf:"varint,15,opt,name=PublishState,proto3" json:"PublishState,omitempty" dc:"发布状态：0-待发布，1-已发布，2-已下线"`     // 发布状态：0-待发布，1-已发布，2-已下线
	IsRecommended    uint32                 `protobuf:"varint,16,opt,name=IsRecommended,proto3" json:"IsRecommended,omitempty" dc:"是否推荐，0-否，1-是"`             // 是否推荐，0-否，1-是
	CreateTime       uint64                 `protobuf:"varint,17,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`                    // 创建时间(毫秒时间戳)
	PublishTime      uint64                 `protobuf:"varint,18,opt,name=PublishTime,proto3" json:"PublishTime,omitempty" dc:"发布时间(毫秒时间戳)"`                  // 发布时间(毫秒时间戳)
	UpdateTime       uint64                 `protobuf:"varint,19,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`                    // 更新时间(毫秒时间戳)
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Videos) Reset() {
	*x = Videos{}
	mi := &file_pbentity_videos_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Videos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Videos) ProtoMessage() {}

func (x *Videos) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_videos_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Videos.ProtoReflect.Descriptor instead.
func (*Videos) Descriptor() ([]byte, []int) {
	return file_pbentity_videos_proto_rawDescGZIP(), []int{0}
}

func (x *Videos) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Videos) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Videos) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *Videos) GetVideoSize() uint64 {
	if x != nil {
		return x.VideoSize
	}
	return 0
}

func (x *Videos) GetVideoDuration() uint32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *Videos) GetVideoFormat() string {
	if x != nil {
		return x.VideoFormat
	}
	return ""
}

func (x *Videos) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *Videos) GetViewCount() uint64 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *Videos) GetShareCount() uint64 {
	if x != nil {
		return x.ShareCount
	}
	return 0
}

func (x *Videos) GetCollectCount() uint64 {
	if x != nil {
		return x.CollectCount
	}
	return 0
}

func (x *Videos) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *Videos) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Videos) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *Videos) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

func (x *Videos) GetPublishState() uint32 {
	if x != nil {
		return x.PublishState
	}
	return 0
}

func (x *Videos) GetIsRecommended() uint32 {
	if x != nil {
		return x.IsRecommended
	}
	return 0
}

func (x *Videos) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Videos) GetPublishTime() uint64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *Videos) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_videos_proto protoreflect.FileDescriptor

const file_pbentity_videos_proto_rawDesc = "" +
	"\n" +
	"\x15pbentity/videos.proto\x12\bpbentity\"\xf4\x04\n" +
	"\x06Videos\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1e\n" +
	"\n" +
	"CategoryId\x18\x02 \x01(\rR\n" +
	"CategoryId\x12\x1a\n" +
	"\bVideoUrl\x18\x03 \x01(\tR\bVideoUrl\x12\x1c\n" +
	"\tVideoSize\x18\x04 \x01(\x04R\tVideoSize\x12$\n" +
	"\rVideoDuration\x18\x05 \x01(\rR\rVideoDuration\x12 \n" +
	"\vVideoFormat\x18\x06 \x01(\tR\vVideoFormat\x12$\n" +
	"\rVideoCoverUrl\x18\a \x01(\tR\rVideoCoverUrl\x12\x1c\n" +
	"\tViewCount\x18\b \x01(\x04R\tViewCount\x12\x1e\n" +
	"\n" +
	"ShareCount\x18\t \x01(\x04R\n" +
	"ShareCount\x12\"\n" +
	"\fCollectCount\x18\n" +
	" \x01(\x04R\fCollectCount\x12 \n" +
	"\vCreatorName\x18\v \x01(\tR\vCreatorName\x12\x16\n" +
	"\x06Author\x18\f \x01(\tR\x06Author\x12\x1e\n" +
	"\n" +
	"AuthorLogo\x18\r \x01(\tR\n" +
	"AuthorLogo\x12*\n" +
	"\x10AuthorAuthStatus\x18\x0e \x01(\rR\x10AuthorAuthStatus\x12\"\n" +
	"\fPublishState\x18\x0f \x01(\rR\fPublishState\x12$\n" +
	"\rIsRecommended\x18\x10 \x01(\rR\rIsRecommended\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x11 \x01(\x04R\n" +
	"CreateTime\x12 \n" +
	"\vPublishTime\x18\x12 \x01(\x04R\vPublishTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x13 \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_videos_proto_rawDescOnce sync.Once
	file_pbentity_videos_proto_rawDescData []byte
)

func file_pbentity_videos_proto_rawDescGZIP() []byte {
	file_pbentity_videos_proto_rawDescOnce.Do(func() {
		file_pbentity_videos_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_videos_proto_rawDesc), len(file_pbentity_videos_proto_rawDesc)))
	})
	return file_pbentity_videos_proto_rawDescData
}

var file_pbentity_videos_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_videos_proto_goTypes = []any{
	(*Videos)(nil), // 0: pbentity.Videos
}
var file_pbentity_videos_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_videos_proto_init() }
func file_pbentity_videos_proto_init() {
	if File_pbentity_videos_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_videos_proto_rawDesc), len(file_pbentity_videos_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_videos_proto_goTypes,
		DependencyIndexes: file_pbentity_videos_proto_depIdxs,
		MessageInfos:      file_pbentity_videos_proto_msgTypes,
	}.Build()
	File_pbentity_videos_proto = out.File
	file_pbentity_videos_proto_goTypes = nil
	file_pbentity_videos_proto_depIdxs = nil
}
