// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiHikmahLanguages {
    uint64 Id         = 1; // 主键ID
    uint64 HikmahId   = 2; // 朝觐智慧ID，关联haji_hikmah.id
    uint32 LanguageId = 3; // 语言ID：0-中文，1-英文，2-印尼语
    string Title      = 4; // 标题
    uint64 CreateTime = 5; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 6; // 更新时间（毫秒时间戳）
}