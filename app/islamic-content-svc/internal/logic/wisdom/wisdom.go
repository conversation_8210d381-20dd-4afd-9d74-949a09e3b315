package wisdom

import (
	"context"
	"halalplus/api/common"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/logic/islamic"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/token"
)

type sWisdom struct {
}

func init() {
	service.RegisterWisdom(New())
}

func New() service.IWisdom {
	return &sWisdom{}
}

func (s *sWisdom) CateList(ctx context.Context, languageId uint32) ([]*v1.WisdomCateItem, error) {
	var items []*v1.WisdomCateItem
	LanguageType := token.GetLanguageId(ctx)
	err := dao.WisdomCate.Ctx(ctx).
		Fields("wisdom_cate.*, wcl.title, wcl.language_id").
		LeftJoin(dao.WisdomCateLanguage.Table()+" wcl", "wcl.wisdom_cate_id = wisdom_cate.id").
		//Where("faq_cate.is_open = ?", 1).
		Where("wcl.language_id = ?", LanguageType).
		Order("wisdom_cate.sort Desc").
		Scan(&items)

	if err != nil {
		return nil, err
	}
	return items, nil
}

func (s *sWisdom) WisdomList(ctx context.Context, req *v1.WisdomListReq) (*v1.WisdomListData, error) {
	var items []*v1.WisdomListItem
	LanguageType := token.GetLanguageId(ctx)
	md := dao.Wisdom.Ctx(ctx).As("w").
		LeftJoin(dao.WisdomLanguage.Table()+" wl", "wl.wisdom_id = w.id").
		//Where("w.is_open = ?", 1).
		Where("w.wisdom_cate_id = ?", req.CateId).
		Where("wl.language_id = ?", LanguageType)
	if req.Keyword != "" {
		md = md.Where("wl.title like ?", "%"+req.Keyword+"%")
	}
	total, err := md.Count()
	if err != nil {
		return nil, err
	}
	md = md.Fields("w.*, wl.title, wl.desc, wl.language_id,wl.image_url")
	err = md.Page(int(req.Page.Page), int(req.Page.Size)).Order("w.id Desc").
		Scan(&items)
	if err != nil {
		return nil, err
	}
	out := &v1.WisdomListData{}
	out.List = items
	out.Page = &common.PageResponse{
		Page:  req.Page.Page,
		Size:  req.Page.Size,
		Total: int32(total),
	}

	return out, nil
}

func (s *sWisdom) WisdomOne(ctx context.Context, languageId uint32, id uint32) ([]*model.NewsInfoOutput, error) {
	//var wisdomOneItem *v1.WisdomOneItem
	//LanguageType := token.GetLanguageId(ctx)
	//err := dao.Wisdom.Ctx(ctx).As("w").
	//	Fields("w.*, wl.title, wl.desc, wl.language_id").
	//	LeftJoin(dao.WisdomLanguage.Table()+" wl", "wl.wisdom_id = w.id").
	//	//Where("faq_question.is_open = ?", 1).
	//	Where("wl.language_id = ?", LanguageType).
	//	Where("w.id = ?", id).
	//	Scan(&wisdomOneItem)
	//if err != nil {
	//	return nil, err
	//}
	//if wisdomOneItem == nil {
	//	return nil, nil
	//}
	//// 查询 分类
	//cateId := wisdomOneItem.WisdomCateId
	//var cate *entity.WisdomCateLanguage
	//err = dao.WisdomCateLanguage.Ctx(ctx).
	//	Where(dao.WisdomCateLanguage.Columns().WisdomCateId, cateId).
	//	Where(dao.WisdomCateLanguage.Columns().LanguageId, languageId).
	//	Scan(&cate)
	//if err != nil {
	//	return nil, err
	//}
	//if cate != nil {
	//	wisdomOneItem.WisdomCateTitle = cate.Title
	//}

	//return wisdomOneItem, nil
	var wisdomOne *entity.Wisdom
	err := dao.Wisdom.Ctx(ctx).Where("id = ?", id).Scan(&wisdomOne)
	if err != nil {
		return nil, err
	}
	if wisdomOne == nil {
		return nil, nil
	}

	islamicLogic := islamic.New()
	out := islamicLogic.NewsInfo(ctx, &model.NewsInfoInput{ArticleId: uint32(wisdomOne.ArticleId)})
	return out, nil
}
