// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// HajiDoaRingkasContent is the golang structure for table haji_doa_ringkas_content.
type HajiDoaRingkasContent struct {
	Id             uint64 `json:"id"             orm:"id"              description:"主键ID"`                      // 主键ID
	DoaId          uint64 `json:"doaId"          orm:"doa_id"          description:"祈祷文ID"`                     // 祈祷文ID
	ContentOrder   int    `json:"contentOrder"   orm:"content_order"   description:"内容排序"`                      // 内容排序
	Title          string `json:"title"          orm:"title"           description:"内容标题（可为空）"`                 // 内容标题（可为空）
	MuqattaAt      string `json:"muqattaAt"      orm:"muqatta_at"      description:"Muqattaʿāt断章字母（有则展示，无不展示）"` // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string `json:"arabicText"     orm:"arabic_text"     description:"阿拉伯文原文"`                    // 阿拉伯文原文
	IndonesianText string `json:"indonesianText" orm:"indonesian_text" description:"印尼语翻译"`                     // 印尼语翻译
	LatinText      string `json:"latinText"      orm:"latin_text"      description:"拉丁音译文本"`                    // 拉丁音译文本
	CreateTime     uint64 `json:"createTime"     orm:"create_time"     description:"创建时间（毫秒时间戳）"`               // 创建时间（毫秒时间戳）
	UpdateTime     uint64 `json:"updateTime"     orm:"update_time"     description:"更新时间（毫秒时间戳）"`               // 更新时间（毫秒时间戳）
}
